<template>
  <div class="smart-menu-panel">
    <!-- 快速访问区域 -->
    <div class="quick-access-section">
      <div class="section-header">
        <h3 class="section-title">
          <MenuIcons name="star" :size="16" />
          <span>快速访问</span>
        </h3>
        <button @click="showQuickAccessSettings = true" class="settings-button">
          <MenuIcons name="settings" :size="14" />
        </button>
      </div>

      <div class="quick-access-grid">
        <button
          v-for="item in quickAccessItems"
          :key="item.id"
          @click="handleItemClick(item)"
          class="quick-access-item"
          :title="item.description"
        >
          <div class="item-icon">
            <MenuIcons :name="item.icon" :size="20" />
          </div>
          <div class="item-label">{{ item.name }}</div>
          <div v-if="item.badge" class="item-badge">{{ item.badge }}</div>
        </button>
      </div>
    </div>

    <!-- 推荐菜单 -->
    <div class="recommendations-section">
      <div class="section-header">
        <h3 class="section-title">
          <MenuIcons name="dashboard" :size="16" />
          <span>为您推荐</span>
        </h3>
        <div class="recommendation-info">
          <span class="info-text">基于使用习惯</span>
        </div>
      </div>

      <div class="recommendations-list">
        <div
          v-for="item in recommendedItems"
          :key="item.id"
          @click="handleItemClick(item)"
          class="recommendation-item"
        >
          <div class="item-main">
            <div class="item-icon">
              <MenuIcons :name="item.icon" :size="18" />
            </div>
            <div class="item-content">
              <div class="item-name">{{ item.name }}</div>
              <div class="item-meta">
                <span class="item-category">{{ item.category }}</span>
                <span class="item-time">{{ item.estimatedTime }}</span>
              </div>
            </div>
          </div>
          <div class="item-actions">
            <button
              @click.stop="toggleFavorite(item.id)"
              class="action-button"
              :class="{ 'active': favorites.includes(item.id) }"
              :title="favorites.includes(item.id) ? '取消收藏' : '添加收藏'"
            >
              <MenuIcons name="star" :size="14" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近访问 -->
    <div v-if="sortedRecentlyVisited.length > 0" class="recent-section">
      <div class="section-header">
        <h3 class="section-title">
          <MenuIcons name="clock" :size="16" />
          <span>最近访问</span>
        </h3>
        <button @click="clearRecentlyVisited" class="clear-button">
          清空
        </button>
      </div>

      <div class="recent-list">
        <div
          v-for="item in sortedRecentlyVisited"
          :key="item.id"
          @click="handleItemClick(item)"
          class="recent-item"
        >
          <div class="item-icon">
            <MenuIcons :name="item.icon" :size="16" />
          </div>
          <div class="item-content">
            <div class="item-name">{{ item.name }}</div>
            <div class="item-time">{{ formatRelativeTime(item.timestamp) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 热门搜索 -->
    <div v-if="topSearches.length > 0" class="popular-searches-section">
      <div class="section-header">
        <h3 class="section-title">
          <MenuIcons name="search" :size="16" />
          <span>热门搜索</span>
        </h3>
      </div>

      <div class="search-tags">
        <button
          v-for="search in topSearches"
          :key="search.query"
          @click="handleSearchClick(search.query)"
          class="search-tag"
        >
          <span>{{ search.query }}</span>
          <span class="search-count">{{ search.count }}</span>
        </button>
      </div>
    </div>

    <!-- 使用统计 -->
    <div class="analytics-section">
      <div class="section-header">
        <h3 class="section-title">
          <MenuIcons name="dashboard" :size="16" />
          <span>使用统计</span>
        </h3>
        <button @click="showAnalytics = !showAnalytics" class="toggle-button">
          {{ showAnalytics ? '收起' : '展开' }}
        </button>
      </div>

      <Transition name="analytics">
        <div v-if="showAnalytics" class="analytics-content">
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">{{ analytics.totalAccess }}</div>
              <div class="stat-label">总访问次数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ analytics.favoriteCount }}</div>
              <div class="stat-label">收藏项目</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ analytics.searchCount }}</div>
              <div class="stat-label">搜索次数</div>
            </div>
          </div>

          <div class="most-used">
            <h4 class="subsection-title">最常使用</h4>
            <div class="most-used-list">
              <div
                v-for="(item, index) in analytics.mostUsedItems.slice(0, 3)"
                :key="item.id"
                class="most-used-item"
              >
                <div class="rank">{{ index + 1 }}</div>
                <div class="item-icon">
                  <MenuIcons :name="item.icon" :size="14" />
                </div>
                <div class="item-name">{{ item.name }}</div>
                <div class="access-count">{{ item.accessCount }}次</div>
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </div>

    <!-- 快速访问设置弹窗 -->
    <Teleport to="body">
      <Transition name="modal">
        <div v-if="showQuickAccessSettings" class="modal-overlay" @click="showQuickAccessSettings = false">
          <div class="modal-content" @click.stop>
            <div class="modal-header">
              <h3>快速访问设置</h3>
              <button @click="showQuickAccessSettings = false" class="close-button">
                <MenuIcons name="close" :size="16" />
              </button>
            </div>
            <div class="modal-body">
              <div class="setting-section">
                <div class="section-title-row">
                  <h4>固定项目</h4>
                  <span class="pinned-count">{{ menuCustomization.pinnedItems.length }}/6 已固定</span>
                </div>
                <p class="setting-description">选择要在快速访问区域固定显示的菜单项（最多6个）</p>
                <div class="pinned-items">
                  <div
                    v-for="item in menuItems"
                    :key="item.id"
                    class="item-option"
                  >
                    <label class="checkbox-label" :class="{ 'disabled': !menuCustomization.pinnedItems.includes(item.id) && menuCustomization.pinnedItems.length >= 6 }">
                      <input
                        type="checkbox"
                        :checked="menuCustomization.pinnedItems.includes(item.id)"
                        :disabled="!menuCustomization.pinnedItems.includes(item.id) && menuCustomization.pinnedItems.length >= 6"
                        @change="handlePinToggle(item.id, $event.target.checked)"
                      />
                      <span class="checkbox-custom"></span>
                      <div class="item-info">
                        <span class="item-name">{{ item.name }}</span>
                        <span class="item-description">{{ item.description }}</span>
                      </div>
                    </label>
                  </div>
                </div>
              </div>

              <!-- 成功提示 -->
              <div v-if="showSaveSuccess" class="save-success">
                <MenuIcons name="star" :size="16" />
                <span>设置已保存</span>
              </div>
            </div>

            <!-- 弹窗底部 -->
            <div class="modal-footer">
              <button @click="resetQuickAccess" class="reset-button">
                重置设置
              </button>
              <div class="footer-actions">
                <button @click="showQuickAccessSettings = false" class="cancel-button">
                  取消
                </button>
                <button @click="saveAndClose" class="save-button">
                  保存并关闭
                </button>
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import { useSmartMenuStore } from '@/stores/smartMenu'
import MenuIcons from './icons/MenuIcons.vue'

const router = useRouter()
const smartMenuStore = useSmartMenuStore()

// 响应式状态
const showAnalytics = ref(false)
const showQuickAccessSettings = ref(false)
const showSaveSuccess = ref(false)

// 从store获取数据 - 使用 storeToRefs 保持响应性
const {
  quickAccessItems,
  recommendedItems,
  sortedRecentlyVisited,
  topSearches,
  favorites,
  menuCustomization,
  menuItems
} = storeToRefs(smartMenuStore)

// 计算分析数据
const analytics = computed(() => smartMenuStore.getAnalytics())

// 事件处理
const handleItemClick = (item) => {
  smartMenuStore.recordVisit(item.id)
  router.push(item.route)
}

const toggleFavorite = (itemId) => {
  smartMenuStore.toggleFavorite(itemId)
}

const clearRecentlyVisited = () => {
  smartMenuStore.clearRecentlyVisited()
}

const handleSearchClick = (query) => {
  // 触发搜索事件
  window.dispatchEvent(new CustomEvent('smart-menu-search', {
    detail: { query }
  }))
}

const handlePinToggle = (itemId, isPinned) => {
  if (isPinned) {
    // 检查是否已达到最大固定数量
    if (menuCustomization.value.pinnedItems.length >= 6) {
      alert('最多只能固定6个菜单项')
      return
    }
    smartMenuStore.pinItem(itemId)
  } else {
    smartMenuStore.unpinItem(itemId)
  }

  // 显示保存成功提示
  showSaveSuccess.value = true
  setTimeout(() => {
    showSaveSuccess.value = false
  }, 2000)
}

// 保存并关闭
const saveAndClose = () => {
  showQuickAccessSettings.value = false
  showSaveSuccess.value = true
  setTimeout(() => {
    showSaveSuccess.value = false
  }, 2000)
}

// 重置快速访问设置
const resetQuickAccess = () => {
  if (confirm('确定要重置快速访问设置吗？这将清除所有固定项目。')) {
    smartMenuStore.resetCustomization()
    showSaveSuccess.value = true
    setTimeout(() => {
      showSaveSuccess.value = false
    }, 2000)
  }
}

// 时间格式化
const formatRelativeTime = (timestamp) => {
  const now = new Date()
  const time = new Date(timestamp)
  const diffInMinutes = Math.floor((now - time) / (1000 * 60))

  if (diffInMinutes < 1) return '刚刚'
  if (diffInMinutes < 60) return `${diffInMinutes}分钟前`

  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `${diffInHours}小时前`

  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) return `${diffInDays}天前`

  return time.toLocaleDateString()
}

// 生命周期
onMounted(() => {
  smartMenuStore.initialize()
})
</script>

<style scoped>
.smart-menu-panel {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

/* 通用样式 */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-neutral-700);
  margin: 0;
}

.dark .section-title {
  color: var(--color-neutral-300);
}

.settings-button,
.clear-button,
.toggle-button {
  padding: 0.25rem 0.5rem;
  background: var(--color-neutral-100);
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  color: var(--color-neutral-600);
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
}

.settings-button:hover,
.clear-button:hover,
.toggle-button:hover {
  background: var(--color-neutral-200);
  transform: scale(1.05);
}

.dark .settings-button,
.dark .clear-button,
.dark .toggle-button {
  background: var(--color-neutral-800);
  border-color: var(--color-neutral-600);
  color: var(--color-neutral-400);
}

.dark .settings-button:hover,
.dark .clear-button:hover,
.dark .toggle-button:hover {
  background: var(--color-neutral-700);
}

/* 快速访问区域 */
.quick-access-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 0.75rem;
}

.quick-access-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 0.5rem;
  background: var(--color-neutral-50);
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
  position: relative;
}

.quick-access-item:hover {
  background: var(--color-neutral-100);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.dark .quick-access-item {
  background: var(--color-neutral-800);
  border-color: var(--color-neutral-600);
}

.dark .quick-access-item:hover {
  background: var(--color-neutral-700);
}

.item-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--color-primary-100), var(--color-secondary-100));
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary-600);
  transition: all var(--duration-200) var(--ease-out);
}

.quick-access-item:hover .item-icon {
  background: linear-gradient(135deg, var(--color-primary-200), var(--color-secondary-200));
  transform: scale(1.1);
}

.dark .item-icon {
  background: linear-gradient(135deg, var(--color-primary-900), var(--color-secondary-900));
  color: var(--color-primary-400);
}

.item-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-neutral-700);
  text-align: center;
  line-height: 1.2;
}

.dark .item-label {
  color: var(--color-neutral-300);
}

.item-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--color-error-500);
  color: white;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-full);
  min-width: 1rem;
  height: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 推荐菜单 */
.recommendation-info {
  font-size: var(--font-size-xs);
  color: var(--color-neutral-500);
  background: var(--color-neutral-100);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-md);
}

.dark .recommendation-info {
  color: var(--color-neutral-400);
  background: var(--color-neutral-800);
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.recommendation-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  background: var(--color-neutral-50);
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
}

.recommendation-item:hover {
  background: var(--color-neutral-100);
  transform: translateX(4px);
  box-shadow: var(--shadow-sm);
}

.dark .recommendation-item {
  background: var(--color-neutral-800);
  border-color: var(--color-neutral-600);
}

.dark .recommendation-item:hover {
  background: var(--color-neutral-700);
}

.item-main {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.recommendation-item .item-icon {
  width: 2rem;
  height: 2rem;
  font-size: 1rem;
}

.item-content {
  flex: 1;
}

.item-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-neutral-900);
  margin-bottom: 0.25rem;
}

.dark .item-name {
  color: var(--color-neutral-100);
}

.item-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: var(--font-size-xs);
  color: var(--color-neutral-500);
}

.dark .item-meta {
  color: var(--color-neutral-400);
}

.item-category {
  background: var(--color-neutral-200);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-sm);
}

.dark .item-category {
  background: var(--color-neutral-700);
}

.item-actions {
  display: flex;
  gap: 0.25rem;
}

.action-button {
  width: 1.75rem;
  height: 1.75rem;
  border-radius: var(--radius-md);
  background: transparent;
  border: 1px solid var(--color-neutral-300);
  color: var(--color-neutral-500);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
}

.action-button:hover {
  background: var(--color-neutral-100);
  border-color: var(--color-primary-300);
  color: var(--color-primary-600);
}

.action-button.active {
  background: var(--color-primary-500);
  border-color: var(--color-primary-500);
  color: white;
}

.dark .action-button {
  border-color: var(--color-neutral-600);
  color: var(--color-neutral-400);
}

.dark .action-button:hover {
  background: var(--color-neutral-700);
  border-color: var(--color-primary-400);
  color: var(--color-primary-400);
}

/* 最近访问 */
.recent-list {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
}

.recent-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0.75rem;
  background: var(--color-neutral-50);
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
}

.recent-item:hover {
  background: var(--color-neutral-100);
  transform: translateX(2px);
}

.dark .recent-item {
  background: var(--color-neutral-800);
  border-color: var(--color-neutral-600);
}

.dark .recent-item:hover {
  background: var(--color-neutral-700);
}

.recent-item .item-icon {
  width: 1.5rem;
  height: 1.5rem;
  background: var(--color-neutral-200);
  color: var(--color-neutral-600);
}

.dark .recent-item .item-icon {
  background: var(--color-neutral-700);
  color: var(--color-neutral-400);
}

.recent-item .item-content {
  flex: 1;
}

.recent-item .item-name {
  font-size: var(--font-size-sm);
  margin-bottom: 0.125rem;
}

.recent-item .item-time {
  font-size: var(--font-size-xs);
  color: var(--color-neutral-500);
}

.dark .recent-item .item-time {
  color: var(--color-neutral-400);
}

/* 热门搜索 */
.search-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.search-tag {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  background: var(--color-neutral-100);
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-xs);
  color: var(--color-neutral-700);
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
}

.search-tag:hover {
  background: var(--color-primary-100);
  border-color: var(--color-primary-300);
  color: var(--color-primary-700);
  transform: scale(1.05);
}

.dark .search-tag {
  background: var(--color-neutral-800);
  border-color: var(--color-neutral-600);
  color: var(--color-neutral-300);
}

.dark .search-tag:hover {
  background: var(--color-primary-900);
  border-color: var(--color-primary-600);
  color: var(--color-primary-300);
}

.search-count {
  background: var(--color-neutral-300);
  color: var(--color-neutral-700);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  min-width: 1.25rem;
  text-align: center;
}

.dark .search-count {
  background: var(--color-neutral-600);
  color: var(--color-neutral-200);
}

/* 使用统计 */
.analytics-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
}

.stat-item {
  text-align: center;
  padding: 1rem 0.5rem;
  background: linear-gradient(135deg, var(--color-primary-50), var(--color-secondary-50));
  border: 1px solid var(--color-primary-200);
  border-radius: var(--radius-lg);
}

.dark .stat-item {
  background: linear-gradient(135deg, var(--color-primary-900), var(--color-secondary-900));
  border-color: var(--color-primary-700);
}

.stat-value {
  font-size: 1.25rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-primary-600);
  margin-bottom: 0.25rem;
}

.dark .stat-value {
  color: var(--color-primary-400);
}

.stat-label {
  font-size: var(--font-size-xs);
  color: var(--color-neutral-600);
  font-weight: var(--font-weight-medium);
}

.dark .stat-label {
  color: var(--color-neutral-400);
}

.subsection-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-neutral-700);
  margin: 0 0 0.5rem 0;
}

.dark .subsection-title {
  color: var(--color-neutral-300);
}

.most-used-list {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
}

.most-used-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: var(--color-neutral-50);
  border-radius: var(--radius-md);
}

.dark .most-used-item {
  background: var(--color-neutral-800);
}

.rank {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: var(--radius-full);
  background: var(--color-primary-500);
  color: white;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  display: flex;
  align-items: center;
  justify-content: center;
}

.most-used-item .item-icon {
  width: 1.25rem;
  height: 1.25rem;
  background: var(--color-neutral-200);
  color: var(--color-neutral-600);
}

.dark .most-used-item .item-icon {
  background: var(--color-neutral-700);
  color: var(--color-neutral-400);
}

.most-used-item .item-name {
  flex: 1;
  font-size: var(--font-size-sm);
  margin-bottom: 0;
}

.access-count {
  font-size: var(--font-size-xs);
  color: var(--color-neutral-500);
  font-weight: var(--font-weight-medium);
}

.dark .access-count {
  color: var(--color-neutral-400);
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.modal-content {
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  width: 90vw;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
}

.dark .modal-content {
  background: rgba(15, 23, 42, 0.98);
  border-color: rgba(51, 65, 85, 0.8);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
}

.dark .modal-header {
  border-bottom-color: rgba(51, 65, 85, 0.6);
}

.modal-header h3 {
  font-size: 1.125rem;
  font-weight: var(--font-weight-semibold);
  color: var(--color-neutral-900);
  margin: 0;
}

.dark .modal-header h3 {
  color: var(--color-neutral-100);
}

.close-button {
  width: 2rem;
  height: 2rem;
  border-radius: var(--radius-md);
  background: var(--color-neutral-100);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
  color: var(--color-neutral-600);
}

.close-button:hover {
  background: var(--color-neutral-200);
  transform: scale(1.05);
}

.dark .close-button {
  background: var(--color-neutral-800);
  color: var(--color-neutral-400);
}

.dark .close-button:hover {
  background: var(--color-neutral-700);
}

.modal-body {
  padding: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
}

.setting-section h4 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-neutral-700);
  margin: 0 0 1rem 0;
}

.dark .setting-section h4 {
  color: var(--color-neutral-300);
}

.pinned-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.item-option {
  display: flex;
  align-items: center;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--radius-md);
  transition: background-color var(--duration-200) var(--ease-out);
}

.checkbox-label:hover {
  background: var(--color-neutral-50);
}

.dark .checkbox-label:hover {
  background: var(--color-neutral-800);
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkbox-custom {
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--color-neutral-300);
  border-radius: var(--radius-sm);
  position: relative;
  transition: all var(--duration-200) var(--ease-out);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
  background: var(--color-primary-500);
  border-color: var(--color-primary-500);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
}

.dark .checkbox-custom {
  border-color: var(--color-neutral-600);
}

.checkbox-label .item-name {
  font-size: var(--font-size-sm);
  color: var(--color-neutral-700);
  margin-bottom: 0;
}

.dark .checkbox-label .item-name {
  color: var(--color-neutral-300);
}

.checkbox-label.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.checkbox-label.disabled input {
  cursor: not-allowed;
}

/* 设置标题行 */
.section-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.pinned-count {
  font-size: 0.75rem;
  color: var(--color-neutral-500);
  background: var(--color-neutral-100);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
}

.dark .pinned-count {
  color: var(--color-neutral-400);
  background: var(--color-neutral-700);
}

/* 设置描述 */
.setting-description {
  font-size: 0.875rem;
  color: var(--color-neutral-600);
  margin-bottom: 1rem;
}

.dark .setting-description {
  color: var(--color-neutral-400);
}

/* 项目信息 */
.item-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.item-description {
  font-size: 0.75rem;
  color: var(--color-neutral-500);
}

.dark .item-description {
  color: var(--color-neutral-500);
}

/* 保存成功提示 */
.save-success {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: var(--color-green-50);
  border: 1px solid var(--color-green-200);
  border-radius: var(--radius-md);
  color: var(--color-green-700);
  font-size: 0.875rem;
  margin-top: 1rem;
  animation: slideIn 0.3s ease-out;
}

.dark .save-success {
  background: var(--color-green-900);
  border-color: var(--color-green-700);
  color: var(--color-green-300);
}

/* 弹窗底部 */
.modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: var(--color-neutral-50);
  border-top: 1px solid var(--color-neutral-200);
}

.dark .modal-footer {
  background: var(--color-neutral-800);
  border-color: var(--color-neutral-600);
}

.footer-actions {
  display: flex;
  gap: 0.75rem;
}

.reset-button,
.cancel-button,
.save-button {
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
}

.reset-button {
  background: transparent;
  border: 1px solid var(--color-red-300);
  color: var(--color-red-600);
}

.reset-button:hover {
  background: var(--color-red-50);
}

.dark .reset-button {
  border-color: var(--color-red-700);
  color: var(--color-red-400);
}

.dark .reset-button:hover {
  background: var(--color-red-900);
}

.cancel-button {
  background: transparent;
  border: 1px solid var(--color-neutral-300);
  color: var(--color-neutral-600);
}

.cancel-button:hover {
  background: var(--color-neutral-50);
}

.dark .cancel-button {
  border-color: var(--color-neutral-600);
  color: var(--color-neutral-400);
}

.dark .cancel-button:hover {
  background: var(--color-neutral-700);
}

.save-button {
  background: var(--color-blue-600);
  border: 1px solid var(--color-blue-600);
  color: white;
}

.save-button:hover {
  background: var(--color-blue-700);
  border-color: var(--color-blue-700);
}

/* 滑入动画 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 动画 */
.analytics-enter-active,
.analytics-leave-active {
  transition: all var(--duration-300) var(--ease-out);
}

.analytics-enter-from {
  opacity: 0;
  transform: translateY(-10px);
  max-height: 0;
}

.analytics-leave-to {
  opacity: 0;
  transform: translateY(-10px);
  max-height: 0;
}

.modal-enter-active,
.modal-leave-active {
  transition: all var(--duration-300) var(--ease-out);
}

.modal-enter-from {
  opacity: 0;
  transform: scale(0.95);
}

.modal-leave-to {
  opacity: 0;
  transform: scale(0.95);
}
</style>