<template>
  <div class="quick-access-panel">
    <!-- 快速访问区域 -->
    <div class="quick-access-section">
      <div class="section-header">
        <h3 class="section-title">
          <MenuIcons name="star" :size="16" />
          <span>快速访问</span>
        </h3>
        <button @click="showQuickAccessSettings = true" class="settings-button">
          <MenuIcons name="settings" :size="14" />
        </button>
      </div>

      <div class="quick-access-grid">
        <button
          v-for="item in quickAccessItems"
          :key="item.id"
          @click="handleItemClick(item)"
          class="quick-access-item"
          :title="item.description"
        >
          <div class="item-icon">
            <MenuIcons :name="item.icon" :size="18" />
          </div>
          <div class="item-label">{{ item.name }}</div>
          <div v-if="item.badge" class="item-badge">{{ item.badge }}</div>
        </button>
        
        <!-- 填充空位，确保4个位置 -->
        <div 
          v-for="n in (4 - quickAccessItems.length)" 
          :key="`empty-${n}`"
          class="quick-access-item empty-slot"
          @click="showQuickAccessSettings = true"
        >
          <div class="item-icon">
            <MenuIcons name="settings" :size="18" />
          </div>
          <div class="item-label">添加</div>
        </div>
      </div>
    </div>

    <!-- 快速访问设置弹窗 -->
    <Teleport to="body">
      <Transition name="modal">
        <div v-if="showQuickAccessSettings" class="modal-overlay" @click="showQuickAccessSettings = false">
          <div class="modal-content" @click.stop>
            <div class="modal-header">
              <h3>快速访问设置</h3>
              <button @click="showQuickAccessSettings = false" class="close-button">
                <MenuIcons name="close" :size="16" />
              </button>
            </div>
            <div class="modal-body">
              <div class="setting-section">
                <div class="section-title-row">
                  <h4>固定项目</h4>
                  <span class="pinned-count">{{ menuCustomization.pinnedItems.length }}/4 已固定</span>
                </div>
                <p class="setting-description">选择要在快速访问区域固定显示的菜单项（最多4个）</p>
                <div class="pinned-items">
                  <div
                    v-for="item in menuItems"
                    :key="item.id"
                    class="item-option"
                  >
                    <label class="checkbox-label" :class="{ 'disabled': !menuCustomization.pinnedItems.includes(item.id) && menuCustomization.pinnedItems.length >= 4 }">
                      <input
                        type="checkbox"
                        :checked="menuCustomization.pinnedItems.includes(item.id)"
                        :disabled="!menuCustomization.pinnedItems.includes(item.id) && menuCustomization.pinnedItems.length >= 4"
                        @change="handlePinToggle(item.id, $event.target.checked)"
                      />
                      <span class="checkbox-custom"></span>
                      <div class="item-info">
                        <span class="item-name">{{ item.name }}</span>
                        <span class="item-description">{{ item.description }}</span>
                      </div>
                    </label>
                  </div>
                </div>
              </div>
              
              <!-- 成功提示 -->
              <div v-if="showSaveSuccess" class="save-success">
                <MenuIcons name="star" :size="16" />
                <span>设置已保存</span>
              </div>
            </div>
            
            <!-- 弹窗底部 -->
            <div class="modal-footer">
              <button @click="resetQuickAccess" class="reset-button">
                重置设置
              </button>
              <div class="footer-actions">
                <button @click="showQuickAccessSettings = false" class="cancel-button">
                  取消
                </button>
                <button @click="saveAndClose" class="save-button">
                  保存并关闭
                </button>
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import { useSmartMenuStore } from '@/stores/smartMenu'
import MenuIcons from './icons/MenuIcons.vue'

const router = useRouter()
const smartMenuStore = useSmartMenuStore()

// 响应式状态
const showQuickAccessSettings = ref(false)
const showSaveSuccess = ref(false)

// 从store获取数据 - 使用 storeToRefs 保持响应性
const {
  quickAccessItems,
  menuCustomization,
  menuItems
} = storeToRefs(smartMenuStore)

// 事件处理
const handleItemClick = (item) => {
  smartMenuStore.recordVisit(item.id)
  router.push(item.route)
}

const handlePinToggle = (itemId, isPinned) => {
  if (isPinned) {
    // 检查是否已达到最大固定数量
    if (menuCustomization.value.pinnedItems.length >= 4) {
      alert('最多只能固定4个菜单项')
      return
    }
    smartMenuStore.pinItem(itemId)
  } else {
    smartMenuStore.unpinItem(itemId)
  }

  // 显示保存成功提示
  showSaveSuccess.value = true
  setTimeout(() => {
    showSaveSuccess.value = false
  }, 2000)
}

// 保存并关闭
const saveAndClose = () => {
  showQuickAccessSettings.value = false
  showSaveSuccess.value = true
  setTimeout(() => {
    showSaveSuccess.value = false
  }, 2000)
}

// 重置快速访问设置
const resetQuickAccess = () => {
  const confirmMessage = '确定要重置快速访问设置吗？\n\n这将清除：\n- 所有固定项目\n- 最近访问记录\n- 搜索历史'
  
  if (confirm(confirmMessage)) {
    smartMenuStore.resetCustomization()
    smartMenuStore.clearRecentlyVisited()
    smartMenuStore.clearSearchHistory()
    
    showSaveSuccess.value = true
    setTimeout(() => {
      showSaveSuccess.value = false
    }, 2000)
  }
}

// 生命周期
import { onMounted } from 'vue'
onMounted(() => {
  smartMenuStore.initialize()
})
</script>

<style scoped>
.quick-access-panel {
  padding: 0.75rem;
  border-bottom: 1px solid var(--color-neutral-200);
  margin-bottom: 0.75rem;
  flex-shrink: 0;
}

.dark .quick-access-panel {
  border-bottom-color: var(--color-neutral-700);
}

/* 通用样式 */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-neutral-700);
  margin: 0;
}

.dark .section-title {
  color: var(--color-neutral-300);
}

.settings-button {
  padding: 0.25rem;
  background: transparent;
  border: 1px solid var(--color-neutral-300);
  border-radius: var(--radius-md);
  color: var(--color-neutral-600);
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
}

.settings-button:hover {
  background: var(--color-neutral-200);
  transform: scale(1.05);
}

.dark .settings-button {
  background: var(--color-neutral-800);
  border-color: var(--color-neutral-600);
  color: var(--color-neutral-400);
}

.dark .settings-button:hover {
  background: var(--color-neutral-700);
}

/* 快速访问区域 */
.quick-access-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.5rem;
}

.quick-access-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.375rem;
  padding: 0.75rem 0.5rem;
  background: var(--color-neutral-50);
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
  position: relative;
  min-height: 80px;
}

.quick-access-item:hover {
  background: var(--color-neutral-100);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.dark .quick-access-item {
  background: var(--color-neutral-800);
  border-color: var(--color-neutral-600);
}

.dark .quick-access-item:hover {
  background: var(--color-neutral-700);
}

.empty-slot {
  background: var(--color-neutral-25);
  border: 2px dashed var(--color-neutral-300);
  color: var(--color-neutral-500);
}

.empty-slot:hover {
  border-color: var(--color-blue-400);
  color: var(--color-blue-600);
  background: var(--color-blue-25);
}

.dark .empty-slot {
  background: var(--color-neutral-900);
  border-color: var(--color-neutral-600);
  color: var(--color-neutral-500);
}

.dark .empty-slot:hover {
  border-color: var(--color-blue-500);
  color: var(--color-blue-400);
  background: var(--color-blue-900);
}

.item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  color: var(--color-neutral-600);
}

.dark .item-icon {
  color: var(--color-neutral-400);
}

.item-label {
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
  text-align: center;
  line-height: 1.2;
  color: var(--color-neutral-700);
}

.dark .item-label {
  color: var(--color-neutral-300);
}

.item-badge {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  background: var(--color-red-500);
  color: white;
  font-size: 0.625rem;
  font-weight: var(--font-weight-bold);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-full);
  min-width: 1rem;
  height: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
}

.dark .modal-content {
  background: var(--color-neutral-800);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--color-neutral-200);
}

.dark .modal-header {
  border-bottom-color: var(--color-neutral-600);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: var(--font-weight-semibold);
  color: var(--color-neutral-900);
}

.dark .modal-header h3 {
  color: var(--color-neutral-100);
}

.close-button {
  padding: 0.5rem;
  background: transparent;
  border: none;
  color: var(--color-neutral-500);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: all var(--duration-200) var(--ease-out);
}

.close-button:hover {
  background: var(--color-neutral-100);
  color: var(--color-neutral-700);
}

.dark .close-button:hover {
  background: var(--color-neutral-700);
  color: var(--color-neutral-300);
}

.modal-body {
  padding: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
}

.setting-section h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: var(--font-weight-semibold);
  color: var(--color-neutral-900);
}

.dark .setting-section h4 {
  color: var(--color-neutral-100);
}

.section-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.pinned-count {
  font-size: 0.75rem;
  color: var(--color-neutral-500);
  background: var(--color-neutral-100);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
}

.dark .pinned-count {
  color: var(--color-neutral-400);
  background: var(--color-neutral-700);
}

.setting-description {
  font-size: 0.875rem;
  color: var(--color-neutral-600);
  margin-bottom: 1rem;
}

.dark .setting-description {
  color: var(--color-neutral-400);
}

.pinned-items {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-height: 300px;
  overflow-y: auto;
}

.item-option {
  display: flex;
  align-items: flex-start;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  width: 100%;
  padding: 0.75rem;
  border-radius: var(--radius-md);
  transition: all var(--duration-200) var(--ease-out);
}

.checkbox-label:hover {
  background: var(--color-neutral-50);
}

.dark .checkbox-label:hover {
  background: var(--color-neutral-700);
}

.checkbox-label.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.checkbox-label.disabled input {
  cursor: not-allowed;
}

.checkbox-custom {
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--color-neutral-300);
  border-radius: var(--radius-sm);
  position: relative;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.checkbox-label input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
  background: var(--color-blue-600);
  border-color: var(--color-blue-600);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '';
  position: absolute;
  left: 0.25rem;
  top: 0.125rem;
  width: 0.25rem;
  height: 0.5rem;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.item-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.item-name {
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  color: var(--color-neutral-900);
}

.dark .item-name {
  color: var(--color-neutral-100);
}

.item-description {
  font-size: 0.75rem;
  color: var(--color-neutral-500);
}

.dark .item-description {
  color: var(--color-neutral-500);
}

.save-success {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: var(--color-green-50);
  border: 1px solid var(--color-green-200);
  border-radius: var(--radius-md);
  color: var(--color-green-700);
  font-size: 0.875rem;
  margin-top: 1rem;
  animation: slideIn 0.3s ease-out;
}

.dark .save-success {
  background: var(--color-green-900);
  border-color: var(--color-green-700);
  color: var(--color-green-300);
}

.modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: var(--color-neutral-50);
  border-top: 1px solid var(--color-neutral-200);
}

.dark .modal-footer {
  background: var(--color-neutral-800);
  border-color: var(--color-neutral-600);
}

.footer-actions {
  display: flex;
  gap: 0.75rem;
}

.reset-button,
.cancel-button,
.save-button {
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
}

.reset-button {
  background: transparent;
  border: 1px solid var(--color-red-300);
  color: var(--color-red-600);
}

.reset-button:hover {
  background: var(--color-red-50);
}

.dark .reset-button {
  border-color: var(--color-red-700);
  color: var(--color-red-400);
}

.dark .reset-button:hover {
  background: var(--color-red-900);
}

.cancel-button {
  background: transparent;
  border: 1px solid var(--color-neutral-300);
  color: var(--color-neutral-600);
}

.cancel-button:hover {
  background: var(--color-neutral-50);
}

.dark .cancel-button {
  border-color: var(--color-neutral-600);
  color: var(--color-neutral-400);
}

.dark .cancel-button:hover {
  background: var(--color-neutral-700);
}

.save-button {
  background: var(--color-blue-600);
  border: 1px solid var(--color-blue-600);
  color: white;
}

.save-button:hover {
  background: var(--color-blue-700);
  border-color: var(--color-blue-700);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 模态框动画 */
.modal-enter-active,
.modal-leave-active {
  transition: all var(--duration-300) var(--ease-out);
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

.modal-enter-to,
.modal-leave-from {
  opacity: 1;
  transform: scale(1);
}
</style>
