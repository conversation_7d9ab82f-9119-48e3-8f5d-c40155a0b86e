<template>
  <div class="sidebar-footer-panel">
    <!-- 推荐菜单 -->
    <div class="recommendations-section">
      <div class="section-header">
        <h3 class="section-title">
          <MenuIcons name="dashboard" :size="16" />
          <span>为您推荐</span>
        </h3>
        <button @click="showRecommendations = !showRecommendations" class="toggle-button">
          <MenuIcons :name="showRecommendations ? 'chevronDown' : 'chevronRight'" :size="14" />
        </button>
      </div>

      <Transition name="collapse">
        <div v-if="showRecommendations" class="recommendations-list">
          <div
            v-if="recommendedItems.length > 0"
            v-for="item in recommendedItems.slice(0, 3)"
            :key="item.id"
            @click="handleItemClick(item)"
            class="recommendation-item"
          >
            <div class="item-icon">
              <MenuIcons :name="item.icon" :size="16" />
            </div>
            <div class="item-content">
              <div class="item-name">{{ item.name }}</div>
              <div class="item-category">{{ item.category }}</div>
            </div>
          </div>

          <div v-else class="empty-recommendations">
            <div class="empty-text">暂无推荐</div>
            <div class="empty-hint">使用菜单后会有智能推荐</div>
          </div>
        </div>
      </Transition>
    </div>

    <!-- 使用统计 -->
    <div class="analytics-section">
      <div class="section-header">
        <h3 class="section-title">
          <MenuIcons name="dashboard" :size="16" />
          <span>使用统计</span>
        </h3>
        <button @click="showAnalytics = !showAnalytics" class="toggle-button">
          <MenuIcons :name="showAnalytics ? 'chevronDown' : 'chevronRight'" :size="14" />
        </button>
      </div>

      <Transition name="collapse">
        <div v-if="showAnalytics" class="analytics-content">
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">{{ analytics.totalAccess }}</div>
              <div class="stat-label">总访问</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ analytics.favoriteCount }}</div>
              <div class="stat-label">收藏</div>
            </div>
          </div>

          <div v-if="analytics.mostUsedItems.length > 0" class="most-used">
            <h4 class="subsection-title">最常使用</h4>
            <div class="most-used-list">
              <div
                v-for="(item, index) in analytics.mostUsedItems.slice(0, 2)"
                :key="item.id"
                class="most-used-item"
                @click="handleItemClick(item)"
              >
                <div class="rank">{{ index + 1 }}</div>
                <div class="item-icon">
                  <MenuIcons :name="item.icon" :size="12" />
                </div>
                <div class="item-name">{{ item.name }}</div>
                <div class="access-count">{{ item.accessCount }}</div>
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import { useSmartMenuStore } from '@/stores/smartMenu'
import MenuIcons from './icons/MenuIcons.vue'

const router = useRouter()
const smartMenuStore = useSmartMenuStore()

// 响应式状态
const showRecommendations = ref(false)
const showAnalytics = ref(false)

// 从store获取数据
const {
  recommendedItems
} = storeToRefs(smartMenuStore)

// 计算分析数据
const analytics = computed(() => smartMenuStore.getAnalytics())

// 事件处理
const handleItemClick = (item) => {
  smartMenuStore.recordVisit(item.id)
  router.push(item.route)
}
</script>

<style scoped>
.sidebar-footer-panel {
  padding: 0.75rem;
  border-top: 1px solid var(--color-neutral-200);
  margin-top: auto;
  flex-shrink: 0;
}

.dark .sidebar-footer-panel {
  border-top-color: var(--color-neutral-700);
}

.recommendations-section,
.analytics-section {
  margin-bottom: 1rem;
}

.recommendations-section:last-child,
.analytics-section:last-child {
  margin-bottom: 0;
}

/* 通用样式 */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  font-weight: var(--font-weight-semibold);
  color: var(--color-neutral-600);
  margin: 0;
}

.dark .section-title {
  color: var(--color-neutral-400);
}

.toggle-button {
  padding: 0.25rem;
  background: transparent;
  border: none;
  color: var(--color-neutral-500);
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
}

.toggle-button:hover {
  color: var(--color-neutral-700);
  transform: scale(1.1);
}

.dark .toggle-button {
  color: var(--color-neutral-500);
}

.dark .toggle-button:hover {
  color: var(--color-neutral-300);
}

/* 推荐列表 */
.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
}

.recommendation-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: var(--color-neutral-25);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
}

.recommendation-item:hover {
  background: var(--color-neutral-100);
}

.dark .recommendation-item {
  background: var(--color-neutral-800);
}

.dark .recommendation-item:hover {
  background: var(--color-neutral-700);
}

.empty-recommendations {
  text-align: center;
  padding: 1rem;
  color: var(--color-neutral-500);
}

.empty-text {
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
}

.empty-hint {
  font-size: 0.6875rem;
  opacity: 0.8;
}

/* 统计内容 */
.analytics-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.stat-item {
  text-align: center;
  padding: 0.5rem;
  background: var(--color-neutral-25);
  border-radius: var(--radius-md);
}

.dark .stat-item {
  background: var(--color-neutral-800);
}

.stat-value {
  font-size: 1rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-neutral-900);
}

.dark .stat-value {
  color: var(--color-neutral-100);
}

.stat-label {
  font-size: 0.625rem;
  color: var(--color-neutral-600);
  margin-top: 0.125rem;
}

.dark .stat-label {
  color: var(--color-neutral-400);
}

.subsection-title {
  font-size: 0.6875rem;
  font-weight: var(--font-weight-semibold);
  color: var(--color-neutral-600);
  margin: 0 0 0.375rem 0;
}

.dark .subsection-title {
  color: var(--color-neutral-400);
}

.most-used-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.most-used-item {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem;
  background: var(--color-neutral-25);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
}

.most-used-item:hover {
  background: var(--color-neutral-100);
}

.dark .most-used-item {
  background: var(--color-neutral-800);
}

.dark .most-used-item:hover {
  background: var(--color-neutral-700);
}

.rank {
  font-size: 0.625rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-neutral-500);
  width: 1rem;
  text-align: center;
}

.item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-neutral-600);
}

.dark .item-icon {
  color: var(--color-neutral-400);
}

.item-name {
  flex: 1;
  font-size: 0.6875rem;
  font-weight: var(--font-weight-medium);
  color: var(--color-neutral-700);
}

.dark .item-name {
  color: var(--color-neutral-300);
}

.item-content {
  flex: 1;
}

.item-category {
  font-size: 0.625rem;
  color: var(--color-neutral-500);
}

.access-count {
  font-size: 0.625rem;
  color: var(--color-neutral-500);
}

/* 折叠动画 */
.collapse-enter-active,
.collapse-leave-active {
  transition: all var(--duration-300) var(--ease-out);
  overflow: hidden;
}

.collapse-enter-from,
.collapse-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-10px);
}

.collapse-enter-to,
.collapse-leave-from {
  opacity: 1;
  max-height: 200px;
  transform: translateY(0);
}
</style>
